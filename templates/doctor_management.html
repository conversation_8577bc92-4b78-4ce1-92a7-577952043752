{% extends "base.html" %}

{% block title %}医生管理 - 医院科室绩效计算器{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="bi bi-people"></i> 医生分组管理</h2>
                <p class="text-muted mb-0">管理医生信息和分组配置</p>
            </div>
            <div>
                <button class="btn btn-success me-2" onclick="showAddDoctorModal()">
                    <i class="bi bi-person-plus"></i> 添加医生
                </button>
                <button class="btn btn-primary" onclick="showAddGroupModal()">
                    <i class="bi bi-plus-circle"></i> 新建分组
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 左侧分组列表 -->
    <div class="col-lg-4 col-md-5">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-collection"></i> 医疗分组
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="list-group list-group-flush" id="groupsList">
                    {% for group in groups %}
                    <div class="list-group-item list-group-item-action group-item" 
                         data-group="{{ group }}" onclick="selectGroup('{{ group }}')">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ group }}</h6>
                                <small class="text-muted">
                                    <span id="count-{{ group }}">加载中...</span>名医生
                                </small>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-danger btn-sm" 
                                        onclick="event.stopPropagation(); deleteGroup('{{ group }}')"
                                        title="删除分组">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- 右侧医生列表 -->
    <div class="col-lg-8 col-md-7">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person-lines-fill"></i> 
                        <span id="selectedGroupTitle">请选择左侧分组查看医生</span>
                    </h5>
                    <div id="doctorActions" style="display: none;">
                        <button class="btn btn-outline-primary btn-sm me-2" onclick="showAddDoctorToGroupModal()">
                            <i class="bi bi-person-plus"></i> 添加医生到此组
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="showAllDoctors()">
                            <i class="bi bi-people"></i> 查看所有医生
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div id="doctorsContainer">
                    <div class="text-center py-5">
                        <i class="bi bi-arrow-left text-muted" style="font-size: 3rem;"></i>
                        <h5 class="mt-3 text-muted">请选择左侧分组</h5>
                        <p class="text-muted">点击左侧分组名称查看该分组的医生列表</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加医生模态框 -->
<div class="modal fade" id="addDoctorModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加医生</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addDoctorForm">
                    <div class="mb-3">
                        <label for="doctorName" class="form-label">医生姓名</label>
                        <input type="text" class="form-control" id="doctorName" required>
                    </div>
                    <div class="mb-3">
                        <label for="doctorGroup" class="form-label">选择分组</label>
                        <select class="form-select" id="doctorGroup" required>
                            <option value="">请选择分组</option>
                            {% for group in groups %}
                            <option value="{{ group }}">{{ group }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addDoctor()">添加医生</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加分组模态框 -->
<div class="modal fade" id="addGroupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新建分组</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addGroupForm">
                    <div class="mb-3">
                        <label for="groupName" class="form-label">分组名称</label>
                        <input type="text" class="form-control" id="groupName" required>
                        <div class="form-text">建议使用"主任医生姓名+组"的格式，如"张三组"</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addGroup()">创建分组</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加医生到指定分组模态框 -->
<div class="modal fade" id="addDoctorToGroupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加医生到 <span id="targetGroupName"></span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addDoctorToGroupForm">
                    <div class="mb-3">
                        <label for="newDoctorName" class="form-label">医生姓名</label>
                        <input type="text" class="form-control" id="newDoctorName" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addDoctorToGroup()">添加医生</button>
            </div>
        </div>
    </div>
</div>

<!-- 移动医生模态框 -->
<div class="modal fade" id="moveDoctorModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">移动医生</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>将医生 <strong id="moveDoctorName"></strong> 从 <strong id="fromGroup"></strong> 移动到：</p>
                <form id="moveDoctorForm">
                    <div class="mb-3">
                        <label for="targetGroup" class="form-label">目标分组</label>
                        <select class="form-select" id="targetGroup" required>
                            <option value="">请选择目标分组</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="moveDoctor()">移动医生</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentSelectedGroup = null;
let currentDoctorToMove = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadGroupCounts();
});

// 加载各分组的医生数量
function loadGroupCounts() {
    fetch('/api/doctor-groups-detail')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                Object.keys(data.groups).forEach(group => {
                    const countElement = document.getElementById(`count-${group}`);
                    if (countElement) {
                        countElement.textContent = data.groups[group].length;
                    }
                });
            }
        })
        .catch(error => {
            console.error('加载分组信息失败:', error);
        });
}

// 选择分组
function selectGroup(groupName) {
    currentSelectedGroup = groupName;
    
    // 更新UI状态
    document.querySelectorAll('.group-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-group="${groupName}"]`).classList.add('active');
    
    // 更新标题
    document.getElementById('selectedGroupTitle').textContent = `${groupName} 的医生`;
    document.getElementById('doctorActions').style.display = 'block';
    
    // 加载该分组的医生
    loadGroupDoctors(groupName);
}

// 加载分组医生
function loadGroupDoctors(groupName) {
    const container = document.getElementById('doctorsContainer');
    container.innerHTML = `
        <div class="text-center py-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载医生列表...</p>
        </div>
    `;
    
    fetch(`/api/group-doctors/${encodeURIComponent(groupName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDoctors(data.doctors, groupName);
            } else {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> 加载失败：${data.error}
                    </div>
                `;
            }
        })
        .catch(error => {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> 加载失败：${error.message}
                </div>
            `;
        });
}

// 显示医生列表
function displayDoctors(doctors, groupName) {
    const container = document.getElementById('doctorsContainer');
    
    if (doctors.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-person-x text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3 text-muted">该分组暂无医生</h5>
                <p class="text-muted">点击上方按钮添加医生到此分组</p>
            </div>
        `;
        return;
    }
    
    let html = '<div class="row g-3">';
    doctors.forEach(doctor => {
        html += `
            <div class="col-md-6 col-lg-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="card-title mb-1">
                                    <i class="bi bi-person"></i> ${doctor}
                                </h6>
                                <small class="text-muted">${groupName}</small>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm" type="button" 
                                        data-bs-toggle="dropdown">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="#" 
                                           onclick="showMoveDoctorModal('${doctor}', '${groupName}')">
                                            <i class="bi bi-arrow-right"></i> 移动到其他组
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-danger" href="#" 
                                           onclick="deleteDoctor('${doctor}', '${groupName}')">
                                            <i class="bi bi-trash"></i> 删除医生
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

// 显示所有医生
function showAllDoctors() {
    currentSelectedGroup = null;

    // 更新UI状态
    document.querySelectorAll('.group-item').forEach(item => {
        item.classList.remove('active');
    });

    document.getElementById('selectedGroupTitle').textContent = '所有医生';
    document.getElementById('doctorActions').style.display = 'none';

    const container = document.getElementById('doctorsContainer');
    container.innerHTML = `
        <div class="text-center py-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2 text-muted">正在加载所有医生...</p>
        </div>
    `;

    fetch('/api/all-doctors')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayAllDoctors(data.doctors);
            } else {
                container.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> 加载失败：${data.error}
                    </div>
                `;
            }
        })
        .catch(error => {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> 加载失败：${error.message}
                </div>
            `;
        });
}

// 显示所有医生列表
function displayAllDoctors(doctors) {
    const container = document.getElementById('doctorsContainer');

    if (doctors.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-person-x text-muted" style="font-size: 3rem;"></i>
                <h5 class="mt-3 text-muted">暂无医生</h5>
                <p class="text-muted">点击上方按钮添加医生</p>
            </div>
        `;
        return;
    }

    let html = '<div class="row g-3">';
    doctors.forEach(doctor => {
        html += `
            <div class="col-md-6 col-lg-4">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="card-title mb-1">
                                    <i class="bi bi-person"></i> ${doctor.name}
                                </h6>
                                <small class="text-muted">${doctor.group}</small>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm" type="button"
                                        data-bs-toggle="dropdown">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" href="#"
                                           onclick="showMoveDoctorModal('${doctor.name}', '${doctor.group}')">
                                            <i class="bi bi-arrow-right"></i> 移动到其他组
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-danger" href="#"
                                           onclick="deleteDoctor('${doctor.name}', '${doctor.group}')">
                                            <i class="bi bi-trash"></i> 删除医生
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';

    container.innerHTML = html;
}

// 显示添加医生模态框
function showAddDoctorModal() {
    const modal = new bootstrap.Modal(document.getElementById('addDoctorModal'));
    document.getElementById('addDoctorForm').reset();
    modal.show();
}

// 显示添加分组模态框
function showAddGroupModal() {
    const modal = new bootstrap.Modal(document.getElementById('addGroupModal'));
    document.getElementById('addGroupForm').reset();
    modal.show();
}

// 显示添加医生到指定分组模态框
function showAddDoctorToGroupModal() {
    if (!currentSelectedGroup) {
        alert('请先选择一个分组');
        return;
    }

    document.getElementById('targetGroupName').textContent = currentSelectedGroup;
    const modal = new bootstrap.Modal(document.getElementById('addDoctorToGroupModal'));
    document.getElementById('addDoctorToGroupForm').reset();
    modal.show();
}

// 显示移动医生模态框
function showMoveDoctorModal(doctorName, fromGroup) {
    currentDoctorToMove = { name: doctorName, fromGroup: fromGroup };

    document.getElementById('moveDoctorName').textContent = doctorName;
    document.getElementById('fromGroup').textContent = fromGroup;

    // 加载其他分组选项
    fetch('/api/doctor-groups')
        .then(response => response.json())
        .then(groups => {
            const select = document.getElementById('targetGroup');
            select.innerHTML = '<option value="">请选择目标分组</option>';

            groups.forEach(group => {
                if (group !== fromGroup) {
                    select.innerHTML += `<option value="${group}">${group}</option>`;
                }
            });
        });

    const modal = new bootstrap.Modal(document.getElementById('moveDoctorModal'));
    modal.show();
}

// 添加医生
function addDoctor() {
    const name = document.getElementById('doctorName').value.trim();
    const group = document.getElementById('doctorGroup').value;

    if (!name || !group) {
        alert('请填写完整信息');
        return;
    }

    const button = event.target;
    showLoading(button);

    fetch('/api/add-doctor', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: name, group: group })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading(button, '添加医生');

        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('addDoctorModal')).hide();
            loadGroupCounts();

            if (currentSelectedGroup === group) {
                loadGroupDoctors(group);
            }

            showAlert('success', '医生添加成功');
        } else {
            showAlert('danger', '添加失败：' + data.error);
        }
    })
    .catch(error => {
        hideLoading(button, '添加医生');
        showAlert('danger', '添加失败：' + error.message);
    });
}

// 添加分组
function addGroup() {
    const name = document.getElementById('groupName').value.trim();

    if (!name) {
        alert('请输入分组名称');
        return;
    }

    const button = event.target;
    showLoading(button);

    fetch('/api/add-group', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: name })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading(button, '创建分组');

        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('addGroupModal')).hide();
            location.reload(); // 重新加载页面以显示新分组
        } else {
            showAlert('danger', '创建失败：' + data.error);
        }
    })
    .catch(error => {
        hideLoading(button, '创建分组');
        showAlert('danger', '创建失败：' + error.message);
    });
}

// 添加医生到指定分组
function addDoctorToGroup() {
    const name = document.getElementById('newDoctorName').value.trim();

    if (!name) {
        alert('请输入医生姓名');
        return;
    }

    const button = event.target;
    showLoading(button);

    fetch('/api/add-doctor', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: name, group: currentSelectedGroup })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading(button, '添加医生');

        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('addDoctorToGroupModal')).hide();
            loadGroupCounts();
            loadGroupDoctors(currentSelectedGroup);
            showAlert('success', '医生添加成功');
        } else {
            showAlert('danger', '添加失败：' + data.error);
        }
    })
    .catch(error => {
        hideLoading(button, '添加医生');
        showAlert('danger', '添加失败：' + error.message);
    });
}

// 移动医生
function moveDoctor() {
    const targetGroup = document.getElementById('targetGroup').value;

    if (!targetGroup) {
        alert('请选择目标分组');
        return;
    }

    const button = event.target;
    showLoading(button);

    fetch('/api/move-doctor', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            doctor: currentDoctorToMove.name,
            fromGroup: currentDoctorToMove.fromGroup,
            toGroup: targetGroup
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoading(button, '移动医生');

        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('moveDoctorModal')).hide();
            loadGroupCounts();

            if (currentSelectedGroup) {
                loadGroupDoctors(currentSelectedGroup);
            }

            showAlert('success', '医生移动成功');
        } else {
            showAlert('danger', '移动失败：' + data.error);
        }
    })
    .catch(error => {
        hideLoading(button, '移动医生');
        showAlert('danger', '移动失败：' + error.message);
    });
}

// 删除医生
function deleteDoctor(doctorName, groupName) {
    if (!confirm(`确定要删除医生"${doctorName}"吗？此操作不可恢复。`)) {
        return;
    }

    fetch('/api/delete-doctor', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ doctor: doctorName })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadGroupCounts();

            if (currentSelectedGroup === groupName) {
                loadGroupDoctors(groupName);
            }

            showAlert('success', '医生删除成功');
        } else {
            showAlert('danger', '删除失败：' + data.error);
        }
    })
    .catch(error => {
        showAlert('danger', '删除失败：' + error.message);
    });
}

// 删除分组
function deleteGroup(groupName) {
    if (!confirm(`确定要删除分组"${groupName}"吗？该分组的医生将被移动到"其他组"。`)) {
        return;
    }

    fetch('/api/delete-group', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ group: groupName })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload(); // 重新加载页面
        } else {
            showAlert('danger', '删除失败：' + data.error);
        }
    })
    .catch(error => {
        showAlert('danger', '删除失败：' + error.message);
    });
}

// 显示提示消息
function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 在页面顶部插入提示
    const container = document.querySelector('.container.mt-4');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
